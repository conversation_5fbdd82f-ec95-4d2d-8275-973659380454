好的，同学你好！我们继续深入计算机硬件的世界。上一节课我们搭建了计算机硬件的宏观框架，了解了从冯·诺依曼到现代计算机的结构演变。

今天，我们就要带上“显微镜”，深入到CPU和主存储器的内部，看看这些部件究竟是如何协同工作的。这部分内容非常关键，是理解“程序如何被一步步执行”的基础，也是408考试中选择题和综合题的高频出题点。

---

# **各个硬件的工作原理**

## **一、 主机核心部件详览**

我们首先聚焦于主机（Host），也就是CPU和主存储器这两个核心。

### **1. 主存储器 (Main Memory)**

主存储器是存放程序和数据的地方。你可以把它想象成一个大型的自动化仓储中心（比如菜鸟驿站）。

#### **a. 基本构成**

主存储器主要由三部分构成：

- **存储体 (Memory Body)**：这是数据的实际存放区域，如同仓库里成排的货架。它由大量的“存储元”构成，每个存储元可以存储1 bit的二进制信息。
    
- **存储地址寄存器 (Memory Address Register, MAR)**：用于存放即将要访问的存储单元的地址。就像你在菜鸟驿站取包裹时，提供给店员的“取件码”。CPU要读写内存时，首先要把地址信息送到MAR。
    
- **存储数据寄存器 (Memory Data Register, MDR)**：这是主存储器与外部（主要是CPU）进行数据交换的临时中转站。它就像驿站的柜台：
    
    - **写入时**：CPU将要写入的数据先放在MDR，再由MDR写入到MAR指定的存储体位置。
        
    - **读取时**：从MAR指定的位置读出的数据，会先暂存在MDR，再由CPU取走。
        

#### **b. 关键参数与计算**

MAR和MDR的位数（宽度）直接决定了主存储器的两个核心指标：

- **MAR的位数决定了存储单元的数量**。
    
    - 如果MAR有
        
        n 位，那么它可以表示 $2^n$ 个不同的地址，对应 $2^n$ 个存储单元。
        
    - 例如，一个有10位MAR的存储器，最多拥有 $2^{10}=1024$ 个存储单元。
        
- **MDR的位数决定了存储字长**。
    
    - 存储字长指的是每个存储单元能存放的二进制位数。这个长度与MDR的位数相等。
        
    - 例如，如果MDR是16位，那么每个存储单元就能存放16 bit的数据，我们称存储字长为16 bit。
        

> **核心公式（必考）**：
> 
> $$存储总容量 = 存储单元个数 \times 存储字长 = 2^{\text{MAR位数}} \times \text{MDR位数}$$

#### **c. 核心概念辨析**

- **存储单元**：每个地址对应一个存储单元，是CPU按地址访问的最小单位。
    
- **存储字 (Word)**：存放在一个存储单元中的二进制代码组合。
    
- **存储字长 (Word Length)**：一个存储字包含的二进制位数。
    

### **2. 运算器 (The Arithmetic/Logic Unit)**

运算器是执行数据加工的核心，内部包含执行具体操作的电路（ALU）和一些重要的寄存器。
- **算术逻辑单元 (ALU)**：这是运算器的核心电路，能实现各种算术运算和逻辑运算。
    
- **累加器 (Accumulator, ACC)**：一个通用的寄存器，通常用于存放操作数或运算结果。比如，执行加法时，一个加数放在ACC，另一个放在X寄存器，结果再存回ACC。
    
- **乘商寄存器 (Multiplier-Quotient Register, MQ)**：在进行乘法和除法运算时，用于存放操作数或运算结果。例如，在乘法中，它可以存放乘数或乘积的低位；在除法中，它可以存放商。
    
- **通用操作数寄存器 (X)**：用于存放操作数，比如加法中的一个加数、减法中的减数等。
    

### **3. 控制器 (The Controller)**

控制器是整个计算机的指挥中心，它通过分析指令，发出各种控制信号来协调其他部件工作。
- **控制单元 (Control Unit, CU)**：控制器的核心，负责分析当前指令的含义，并根据指令向其他部件（如ALU、主存）发出控制信号。
    
- **指令寄存器 (Instruction Register, IR)**：用于存放**当前**正在执行的指令。当一条指令从主存被取到CPU后，就暂存在IR中，等待CU来分析。
    
- **程序计数器 (Program Counter, PC)**：用于存放**下一条**将要执行的指令的地址。它具有自动加1的功能，从而保证程序能按顺序执行。
    

## **二、 计算机的完整工作流程**

了解了这些部件后，我们就可以把它们串联起来，看看一条高级语言程序

`y = a * b + c` 是如何被计算机一步步执行的。

### **1. 指令周期 (The Instruction Cycle)**

计算机执行一条指令的过程，被称为一个**指令周期**。它通常包含两个主要阶段：

- **取指周期 (Fetch Cycle)**：从主存中取出指令并送到控制器的过程。这个过程对于所有指令来说都是相同的。
    
    1. `(PC) → MAR`：将程序计数器（PC）中存放的指令地址，送到存储地址寄存器（MAR）。
        
    2. `M(MAR) → MDR`：主存储器根据MAR中的地址，到存储体中找到对应的指令，读出并放入存储数据寄存器（MDR）。
        
    3. `(MDR) → IR`：将MDR中的指令送到指令寄存器（IR）。
        
    4. `(PC) + 1 → PC`：PC内容自动加1，指向下一条指令的地址。
        
- **执行周期 (Execute Cycle)**：分析并执行指令的过程。这个过程因指令不同而异。
    
    1. `OP(IR) → CU`：CU分析IR中指令的操作码部分，确定要执行什么操作（如取数、乘法、存数）。
        
    2. CU根据指令类型，发出控制信号，指挥运算器、存储器等完成相应操作。
        

### **2. 案例分析：`y = a*b+c` 的执行**

我们通过追踪这个程序在硬件中的执行微操作，来加深理解。

1. **初始状态**：经过编译后，程序的指令和数据都已存入主存，PC指向第一条指令（地址为0）。
    
2. **执行“取数 a”指令**：
    
    - **取指**：CPU执行公共的取指操作，将地址0处的“取数”指令放入IR，同时PC变为1。
        
    - **执行**：CU分析后发现是“取数”指令。它将指令中的地址码（a的地址，即5）送至MAR，从主存中取出数据2，并通过MDR最终送到累加器ACC。执行完毕，ACC中的值为2。
        
3. **执行“乘法 b”指令**：
    
    - **取指**：PC值为1，CPU取出地址1处的“乘法”指令，PC变为2。
        
    - **执行**：CU分析为“乘法”指令。从指令地址码（b的地址，即6）处取出数据3，放入MQ寄存器。ALU执行ACC和MQ中两个数的乘法，结果6存回ACC。
        
4. **执行“加法 c”指令**：流程类似，执行后ACC中的值变为 $6+1=7$。
    
5. **执行“存数 y”指令**：
    
    - **取指**：PC值为3，CPU取出地址3处的“存数”指令，PC变为4。
        
    - **执行**：CU分析为“存数”指令。将ACC中的数据7通过MDR写入指令地址码（y的地址，即8）指定的主存单元中。
        
6. **执行“停机”指令**：CPU取指后，CU分析得知是“停机”指令，程序执行结束。
    

## **三、 独家见解与备考指南**

### **1. 核心问题：CPU如何区分指令和数据？**

这是一个经典的考研问题。指令和数据都以二进制形式存放在主存中，CPU区分它们的依据是

**指令周期的不同阶段**。

- 在**取指周期**内，从内存读出的信息被CPU一律视为**指令**。
    
- 在**执行周期**内，根据指令的需要，从内存读出的信息则被视为**数据**。
    

简单来说，就是“在什么时间去取”决定了取出来东西的身份。

### **2. 考点计算题：牢记存储容量公式**

关于主存容量的计算题几乎是必考的。

- **题目形式**：“某计算机存储字长16位，其存储容量为64KB，MAR和MDR的位数分别是多少？”
    
- **解题方法**：
    
    - **MDR位数**：等于存储字长，所以MDR是16位。
        
    - **MAR位数**：存储容量 = 存储单元数 $\times$ 存储字长。
        
        - $64KB = 64 \times 1024$ 字节。
            
        - 因为字长是16位 $= 2$ 字节，所以存储单元数 $= 64 \times 1024 / 2 = 32 \times 1024 = 2^5 \times 2^{10} = 2^{15}$。
            
        - 存储单元数是 $2^{15}$，所以MAR需要15位。
            

### **3. 记忆技巧：巧用类比**

- **主存**：就是“菜鸟驿站”，MAR是取件码，MDR是柜台，存储体是货架。
    
- **控制器三剑客**：
    
    - **PC (Program Counter)**：像一个“报幕员”，永远指向下一个节目（指令）的地址。
        
    - **IR (Instruction Register)**：像“导演的监视器”，显示着当前正在执行的指令。
        
    - **CU (Control Unit)**：就是“导演”本人，看着监视器（IR）里的内容，然后通过对讲机（控制信号）指挥全场。
        

这部分内容非常细节，但规律性很强。务必亲手画出硬件结构图，并模拟一两条简单指令（如取数、存数、加法）的完整执行流程，把数据在各个寄存器和部件间的流转路径搞清楚，才能做到烂熟于心。加油！