好的，同学你好！欢迎来到我们第一章的最后一讲。今天，我们要讨论一个非常实际的话题：**计算机的性能指标**。

学习了计算机的组成和工作原理后，我们自然会问：如何衡量一台计算机的快慢？是看CPU主频？还是看内存大小？这一节课，我们将学习一套科学的、量化的指标来评价计算机各个部件乃至整个系统的性能。这部分内容是408考研计算题的重灾区，务必掌握其中的核心公式。

---

# **计算机的性能指标**

评价一台计算机的性能，不能只看单一指标，而是要从存储器、CPU、系统整体等多个维度进行综合考量。

## **一、 存储器的性能指标**

### **1. 存储容量 (Storage Capacity)**

- **定义**：指主存储器能存放的二进制信息总量。
    
- **计算公式**：
    
    - 总容量=$存储单元个数 \times 存储字长$
        
    - 其中，存储单元的数量由
        
        **MAR**的位数决定，即 `存储单元个数` =$2^{MAR位数}$。
        
    - 存储字长由
        
        **MDR**的位数决定，即 `存储字长` =$MDR位数$。
        
- **举例**：若MAR为32位，MDR为8位，则总容量为 $2^{32}\times8$ bit=$2^{32}$ Byte=4 GB。
    

### **2. 数量单位辨析**

在描述存储容量或文件大小时，我们使用的数量单位K、M、G、T等，通常基于**2的幂次**：

- 1K=$2^{10}$
    
- 1M=$2^{20}$
    
- 1G=$2^{30}$
    
- 1T=$2^{40}$
    

## **二、 CPU的性能指标**

### **1. 时间与频率**

- **CPU时钟周期 (Clock Cycle)**：是CPU中最小的时间单位，CPU的每一个动作至少需要一个时钟周期。单位是秒、微秒、纳秒等。
    
- **CPU主频 (Clock Frequency)**：它是CPU时钟周期的倒数，表示CPU内数字脉冲信号振荡的频率。单位是赫兹(Hz)。
    
    - 主频=$\frac{1}{CPU时钟周期}$
        

### **2. 核心性能公式**

- **CPI (Clock cycles Per Instruction)**：指执行一条指令平均需要的时钟周期数。这是一个平均值，因为不同指令的复杂度不同，其实际CPI也可能不同。
    
- **CPU执行时间 (CPU Execution Time)**：是衡量CPU性能最核心、最准确的指标，指运行一个程序所花费的时间。
    
    - CPU执行时间=$\frac{指令条数 \times 平均CPI}{主频}$
        
- **IPS (Instructions Per Second)**：指CPU每秒能执行多少条指令。
    
    - IPS=$\frac{主频}{平均CPI}=\frac{指令条数}{CPU执行时间}$
        
- **FLOPS (Floating-point Operations Per Second)**：指每秒执行多少次浮点运算，常用来衡量计算机在科学计算领域的性能。
    

### **3. 数量单位辨析**

在描述频率、速率（如IPS、FLOPS）时，使用的数量单位K、M、G、T等，基于**10的幂次**：

- K=$10^3$
    
- M=$10^6$
    
- G=$10^9$
    
- T=$10^{12}$
    
- 更新的单位还有 P(Peta,
    
    $10^{15}$), E(Exa, $10^{18}$), Z(Zetta, $10^{21}$)。
    

## **三、 系统整体的性能指标**

- **数据通路带宽 (Data Path Bandwidth)**：指数据总线一次所能并行传送的信息位数。
    
- **吞吐量 (Throughput)**：指系统在单位时间内成功处理请求的数量。它受系统中各个环节的影响，但主要取决于主存的存取周期。
    
- **响应时间 (Response Time)**：指从用户发出请求，到系统对该请求做出响应并获得结果的全部等待时间。它包含了CPU执行时间和各类等待时间（如磁盘访问、I/O操作等）。
    
- **基准程序 (Benchmark Program)**：是一种用来测量计算机处理速度的实用程序。用户可以通过运行它来对比不同计算机的性能，例如我们熟知的“跑分软件”。
    

## **四、 独家见解与备考指南**

### **1. 见解：戳破“唯主频论”的泡沫**

很多同学，甚至一些商家都喜欢用“主频”来衡量CPU的好坏。但这是片面的。

- **主频高的CPU一定快吗？不一定。** 一个主频高但设计落后（导致平均CPI很高）的CPU，其综合性能可能还不如一个主频稍低但设计优秀（CPI很低）的CPU。
    
- **CPI相同，主频高的就一定快吗？也不一定。**这还要看指令系统。如果CPU A不支持乘法指令，需要用多次加法模拟，而CPU B支持硬件乘法指令，那么在执行乘法运算时，CPU A需要执行的指令总数会远超CPU B，即便A的主频更高，也可能会更慢。
    

> **核心结论**：评价CPU性能，必须综合考虑三个因素：**主频、CPI、指令条数**。三者共同决定了最终的CPU执行时间。

### **2. 备考方法：单位换算，务必细心！**

这是计算题中最容易失分的地方。请务必牢记两种单位体系的差异：

- **用于存储容量/文件大小**：K、M、G、T是 **2 的幂次** ($2^{10},2^{20},...$)。
    
- **用于频率和速率**：K、M、G、T是 **10 的幂次** ($10^3,10^6,...$)。
    

实战演练：

一个带宽为 100 Mbps 的网络，下载一个 4 MB 的文件需要多长时间？

1. 带宽换算：`100 Mbps` = $100 \times 10^6$ bit/s
    
2. 文件大小换算：`4 MB` = $4 \times 2^{20}$ Byte = $4 \times 2^{20} \times 8$ bit
    
3. 计算时间：Time=$\frac{4 \times 2^{20} \times 8}{100 \times 10^6}\approx0.335$ 秒。
    
    如果你在计算时混用了 $10^6$ 和 $2^{20}$，结果就会出错。
    

### **3. 见解：动态指标 vs. 静态指标**

- **静态指标**（如主频、数据通路带宽）描述了计算机硬件的**理论峰值性能**。
    
- **动态指标**（如吞吐量、响应时间、基准程序跑分）描述了计算机在**实际运行负载**下的性能。
    
- 考研更侧重于考察基于核心公式的静态性能计算，但也要理解动态指标的含义，因为它们更能反映用户的真实体验。
    

性能指标这一章公式多，概念也多，但只要你抓住了`CPU执行时间`这个核心公式，并理清了各个指标之间的关系，拿下它并不困难。加油！